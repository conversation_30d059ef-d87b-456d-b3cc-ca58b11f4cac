"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Progress } from "../ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { AlertCircle, CheckCircle, Copy, RefreshCw } from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import DomainCardLink from "./DomainCardLink";

interface AssessmentDashboardProps {
  userName?: string;
  coupleCode?: string;
  partnerName?: string;
  isConnected?: boolean;
  completedDomains?: string[];
}

const AssessmentDashboard = ({
  userName = "John",
  coupleCode = "",
  partnerName = "",
  isConnected = false,
  completedDomains = [],
}: AssessmentDashboardProps) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [generatedCode, setGeneratedCode] = useState(coupleCode || "");
  const [inputCode, setInputCode] = useState("");
  const [connectionStatus, setConnectionStatus] = useState<
    "idle" | "pending" | "connected" | "error"
  >(isConnected ? "connected" : "idle");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Assessment domains
  const domains = [
    {
      id: "vision",
      title: "Vision",
      description: "Life goals and future plans",
      icon: "🔭",
      completed: completedDomains.includes("vision"),
      status: completedDomains.includes("vision") ? "completed" : "not-started",
    },
    {
      id: "finances",
      title: "Finances",
      description: "Money management and financial goals",
      icon: "💰",
      completed: completedDomains.includes("finances"),
      status: completedDomains.includes("finances")
        ? "completed"
        : "not-started",
    },
    {
      id: "parenting",
      title: "Parenting",
      description: "Child-rearing philosophies and approaches",
      icon: "👶",
      completed: completedDomains.includes("parenting"),
      status: completedDomains.includes("parenting")
        ? "completed"
        : "not-started",
    },
    {
      id: "communication",
      title: "Communication",
      description: "Styles and patterns of interaction",
      icon: "💬",
      completed: completedDomains.includes("communication"),
      status: completedDomains.includes("communication")
        ? "completed"
        : "not-started",
    },
    {
      id: "roles",
      title: "Roles",
      description: "Functions and responsibilities in marriage",
      icon: "🔄",
      completed: completedDomains.includes("roles"),
      status: completedDomains.includes("roles") ? "completed" : "not-started",
    },
    {
      id: "sexuality",
      title: "Sexuality",
      description: "Intimacy and physical relationship",
      icon: "❤️",
      completed: completedDomains.includes("sexuality"),
      status: completedDomains.includes("sexuality")
        ? "completed"
        : "not-started",
    },
    {
      id: "spirituality",
      title: "Spirituality",
      description: "Faith practices and spiritual growth",
      icon: "✝️",
      completed: completedDomains.includes("spirituality"),
      status: completedDomains.includes("spirituality")
        ? "completed"
        : "not-started",
    },
    {
      id: "darkside",
      title: "Dark Side",
      description: "Potential challenges and negative patterns",
      icon: "🌑",
      completed: completedDomains.includes("darkside"),
      status: completedDomains.includes("darkside")
        ? "completed"
        : "not-started",
    },
  ];

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (completedDomains.length / domains.length) * 100,
  );

  // Generate a new couple code
  const generateCoupleCode = async () => {
    setLoading(true);
    setErrorMessage("");

    try {
      const response = await fetch("/api/couples/generate-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate code");
      }

      setGeneratedCode(data.code);
    } catch (error) {
      console.error("Error generating code:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to generate code");
    } finally {
      setLoading(false);
    }
  };

  // Copy code to clipboard
  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
    // In a real app, show a toast notification
  };

  // Connect with partner using code
  const connectWithPartner = async () => {
    if (inputCode.length !== 6) {
      setErrorMessage("Please enter a valid 6-character code");
      return;
    }

    setLoading(true);
    setConnectionStatus("pending");
    setErrorMessage("");

    try {
      const response = await fetch("/api/couples/connect", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ code: inputCode }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to connect with partner");
      }

      setConnectionStatus("connected");
      setInputCode("");
      // Optionally refresh the page or update the UI with partner info
      window.location.reload();
    } catch (error) {
      console.error("Error connecting with partner:", error);
      setConnectionStatus("error");
      setErrorMessage(error instanceof Error ? error.message : "Failed to connect with partner");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-background w-full max-w-7xl mx-auto p-4 md:p-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Assessment Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {userName}. Continue your marriage assessment journey.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setActiveTab("overview")}>
            Overview
          </Button>
          <Button variant="outline" onClick={() => setActiveTab("connect")}>
            Connect
          </Button>
          {progressPercentage > 0 && (
            <Button variant="outline" onClick={() => setActiveTab("results")}>
              Results
            </Button>
          )}
          <Button variant="default" asChild>
            <Link href="/couple/dashboard">Couple Dashboard</Link>
          </Button>
        </div>
      </div>

      {activeTab === "overview" && (
        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Assessment Progress</CardTitle>
              <CardDescription>
                Complete all 8 domains to get comprehensive insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    {completedDomains.length} of {domains.length} completed
                  </span>
                  <span>{progressPercentage}%</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {connectionStatus === "connected" ? (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription>
                You are connected with {partnerName || "your partner"}. Complete
                your assessments to view compatibility results.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Connect with your partner to compare assessment results and get
                compatibility insights.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {domains.map((domain) => (
              <DomainCardLink
                key={domain.id}
                title={domain.title}
                description={domain.description}
                icon={domain.icon}
                status={domain.status}
                domainId={domain.id}
              />
            ))}
          </div>
        </div>
      )}

      {activeTab === "connect" && (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Connect with Your Partner</CardTitle>
            <CardDescription>
              Generate a code to share with your partner or enter the code they
              shared with you
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="generate" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="generate">Generate Code</TabsTrigger>
                <TabsTrigger value="enter">Enter Code</TabsTrigger>
              </TabsList>

              <TabsContent value="generate" className="space-y-4">
                <div className="mt-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <Input
                      value={generatedCode}
                      readOnly
                      className="font-mono text-center text-lg"
                      placeholder="No code generated"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={copyCodeToClipboard}
                      disabled={!generatedCode}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button
                    onClick={generateCoupleCode}
                    className="w-full"
                    disabled={loading}
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    {loading ? 'Generating...' : 'Generate New Code'}
                  </Button>
                  <p className="text-sm text-muted-foreground text-center">
                    Share this code with your partner so they can connect with
                    you
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="enter" className="space-y-4">
                <div className="mt-6 space-y-4">
                  <Input
                    value={inputCode}
                    onChange={(e) => setInputCode(e.target.value)}
                    className="font-mono text-center text-lg"
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                  />
                  <Button
                    onClick={connectWithPartner}
                    className="w-full"
                    disabled={
                      loading || connectionStatus === "pending" || inputCode.length !== 6
                    }
                  >
                    {connectionStatus === "pending" || loading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      "Connect with Partner"
                    )}
                  </Button>

                  {(connectionStatus === "error" || errorMessage) && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        {errorMessage || "Invalid code. Please check and try again."}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {activeTab === "results" && (
        <Card>
          <CardHeader>
            <CardTitle>Assessment Results</CardTitle>
            <CardDescription>
              {connectionStatus === "connected"
                ? "View your compatibility results and insights"
                : "Connect with your partner to view compatibility results"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {connectionStatus === "connected" ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  {completedDomains.length === domains.length
                    ? "All assessments completed! View your detailed results below."
                    : "Complete all assessment domains to view detailed compatibility results."}
                </p>
                <Button disabled={completedDomains.length !== domains.length}>
                  View Detailed Results
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  You need to connect with your partner first to view
                  compatibility results.
                </p>
                <Button onClick={() => setActiveTab("connect")}>
                  Connect with Partner
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AssessmentDashboard;
