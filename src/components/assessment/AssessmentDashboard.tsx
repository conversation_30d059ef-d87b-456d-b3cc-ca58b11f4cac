"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Progress } from "../ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { AlertCircle, CheckCircle, Copy, RefreshCw } from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import DomainCardLink from "./DomainCardLink";
import { createClient } from "@/lib/supabase/client";

interface AssessmentDashboardProps {
  userName?: string;
  coupleCode?: string;
  partnerName?: string;
  isConnected?: boolean;
  completedDomains?: string[];
}

const AssessmentDashboard = ({
  userName = "John",
  coupleCode = "",
  partnerName = "",
  isConnected = false,
  completedDomains = [],
}: AssessmentDashboardProps) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [generatedCode, setGeneratedCode] = useState(coupleCode || "");
  const [inputCode, setInputCode] = useState("");
  const [connectionStatus, setConnectionStatus] = useState<
    "idle" | "pending" | "connected" | "error"
  >(isConnected ? "connected" : "idle");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Assessment domains
  const domains = [
    {
      id: "vision",
      title: "Vision",
      description: "Life goals and future plans",
      icon: "🔭",
      completed: completedDomains.includes("vision"),
      status: completedDomains.includes("vision") ? "completed" : "not-started",
    },
    {
      id: "finances",
      title: "Finances",
      description: "Money management and financial goals",
      icon: "💰",
      completed: completedDomains.includes("finances"),
      status: completedDomains.includes("finances")
        ? "completed"
        : "not-started",
    },
    {
      id: "parenting",
      title: "Parenting",
      description: "Child-rearing philosophies and approaches",
      icon: "👶",
      completed: completedDomains.includes("parenting"),
      status: completedDomains.includes("parenting")
        ? "completed"
        : "not-started",
    },
    {
      id: "communication",
      title: "Communication",
      description: "Styles and patterns of interaction",
      icon: "💬",
      completed: completedDomains.includes("communication"),
      status: completedDomains.includes("communication")
        ? "completed"
        : "not-started",
    },
    {
      id: "roles",
      title: "Roles",
      description: "Functions and responsibilities in marriage",
      icon: "🔄",
      completed: completedDomains.includes("roles"),
      status: completedDomains.includes("roles") ? "completed" : "not-started",
    },
    {
      id: "sexuality",
      title: "Sexuality",
      description: "Intimacy and physical relationship",
      icon: "❤️",
      completed: completedDomains.includes("sexuality"),
      status: completedDomains.includes("sexuality")
        ? "completed"
        : "not-started",
    },
    {
      id: "spirituality",
      title: "Spirituality",
      description: "Faith practices and spiritual growth",
      icon: "✝️",
      completed: completedDomains.includes("spirituality"),
      status: completedDomains.includes("spirituality")
        ? "completed"
        : "not-started",
    },
    {
      id: "darkside",
      title: "Dark Side",
      description: "Potential challenges and negative patterns",
      icon: "🌑",
      completed: completedDomains.includes("darkside"),
      status: completedDomains.includes("darkside")
        ? "completed"
        : "not-started",
    },
  ];

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (completedDomains.length / domains.length) * 100,
  );

  // Generate a new couple code
  const generateCoupleCode = async () => {
    setLoading(true);
    setErrorMessage("");

    try {
      const supabase = createClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error("You must be logged in to generate a code");
      }

      // Check if user is already in a couple
      const { data: existingCouple } = await supabase
        .from("couples")
        .select("couple_id")
        .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
        .single();

      if (existingCouple) {
        throw new Error("You are already connected with a partner");
      }

      // Deactivate any existing active codes for this user
      await supabase
        .from("couple_invitation_codes")
        .update({ is_active: false })
        .eq("creator_user_id", user.id)
        .eq("is_active", true);

      // Generate a unique 6-character code
      let code: string;
      let isUnique = false;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        code = Math.random().toString(36).substring(2, 8).toUpperCase();

        // Check if code already exists
        const { data: existingCode } = await supabase
          .from("couple_invitation_codes")
          .select("id")
          .eq("code", code)
          .eq("is_active", true)
          .single();

        isUnique = !existingCode;
        attempts++;
      } while (!isUnique && attempts < maxAttempts);

      if (!isUnique) {
        throw new Error("Failed to generate unique code. Please try again.");
      }

      // Create the invitation code
      const { data: invitationCode, error: codeError } = await supabase
        .from("couple_invitation_codes")
        .insert({
          code,
          creator_user_id: user.id,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        })
        .select()
        .single();

      if (codeError) {
        console.error("Error creating invitation code:", codeError);
        throw new Error("Failed to create invitation code");
      }

      setGeneratedCode(invitationCode.code);
    } catch (error) {
      console.error("Error generating code:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to generate code");
    } finally {
      setLoading(false);
    }
  };

  // Copy code to clipboard
  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
    // In a real app, show a toast notification
  };

  // Connect with partner using code
  const connectWithPartner = async () => {
    if (inputCode.length !== 6) {
      setErrorMessage("Please enter a valid 6-character code");
      return;
    }

    setLoading(true);
    setConnectionStatus("pending");
    setErrorMessage("");

    try {
      const supabase = createClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error("You must be logged in to connect");
      }

      // Check if user is already in a couple
      const { data: existingCouple } = await supabase
        .from("couples")
        .select("couple_id")
        .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
        .single();

      if (existingCouple) {
        throw new Error("You are already connected with a partner");
      }

      // Find the invitation code
      console.log("Searching for code:", inputCode.toUpperCase());

      // Use RPC function or direct query with proper permissions
      const { data: invitationCode, error: codeError } = await supabase
        .from("couple_invitation_codes")
        .select("*")
        .eq("code", inputCode.toUpperCase())
        .eq("is_active", true)
        .gte("expires_at", new Date().toISOString())
        .single();

      console.log("Code search result:", { invitationCode, codeError });

      if (codeError || !invitationCode) {
        // Let's also check if code exists but is inactive
        const { data: anyCode } = await supabase
          .from("couple_invitation_codes")
          .select("*")
          .eq("code", inputCode.toUpperCase())
          .single();

        console.log("Any code with this value:", anyCode);

        if (anyCode && !anyCode.is_active) {
          throw new Error("This invitation code has been used or deactivated");
        }

        throw new Error("Invalid or expired invitation code");
      }

      // Check if code has expired
      if (new Date(invitationCode.expires_at) < new Date()) {
        throw new Error("Invitation code has expired");
      }

      // Check if code has already been used
      if (invitationCode.used_by_user_id) {
        throw new Error("Invitation code has already been used");
      }

      // Check if user is trying to use their own code
      if (invitationCode.creator_user_id === user.id) {
        throw new Error("You cannot use your own invitation code");
      }

      // Check if creator is already in a couple
      const { data: creatorCouple } = await supabase
        .from("couples")
        .select("couple_id")
        .or(`user_id_1.eq.${invitationCode.creator_user_id},user_id_2.eq.${invitationCode.creator_user_id}`)
        .single();

      if (creatorCouple) {
        throw new Error("The code creator is already connected with someone else");
      }

      // Create couple
      const { data: couple, error: coupleError } = await supabase
        .from("couples")
        .insert({
          user_id_1: invitationCode.creator_user_id,
          user_id_2: user.id,
        })
        .select()
        .single();

      if (coupleError) {
        console.error("Error creating couple:", coupleError);
        throw new Error("Failed to create couple connection");
      }

      // Update invitation code as used
      await supabase
        .from("couple_invitation_codes")
        .update({
          used_by_user_id: user.id,
          used_at: new Date().toISOString(),
          is_active: false,
        })
        .eq("id", invitationCode.id);

      setConnectionStatus("connected");
      setInputCode("");
      // Refresh the page to update the UI
      window.location.reload();
    } catch (error) {
      console.error("Error connecting with partner:", error);
      setConnectionStatus("error");
      setErrorMessage(error instanceof Error ? error.message : "Failed to connect with partner");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-background w-full max-w-7xl mx-auto p-4 md:p-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Assessment Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {userName}. Continue your marriage assessment journey.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setActiveTab("overview")}>
            Overview
          </Button>
          <Button variant="outline" onClick={() => setActiveTab("connect")}>
            Connect
          </Button>
          {progressPercentage > 0 && (
            <Button variant="outline" onClick={() => setActiveTab("results")}>
              Results
            </Button>
          )}
          <Button variant="default" asChild>
            <Link href="/couple/dashboard">Couple Dashboard</Link>
          </Button>
        </div>
      </div>

      {activeTab === "overview" && (
        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Assessment Progress</CardTitle>
              <CardDescription>
                Complete all 8 domains to get comprehensive insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    {completedDomains.length} of {domains.length} completed
                  </span>
                  <span>{progressPercentage}%</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {connectionStatus === "connected" ? (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription>
                You are connected with {partnerName || "your partner"}. Complete
                your assessments to view compatibility results.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Connect with your partner to compare assessment results and get
                compatibility insights.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {domains.map((domain) => (
              <DomainCardLink
                key={domain.id}
                title={domain.title}
                description={domain.description}
                icon={domain.icon}
                status={domain.status}
                domainId={domain.id}
              />
            ))}
          </div>
        </div>
      )}

      {activeTab === "connect" && (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Connect with Your Partner</CardTitle>
            <CardDescription>
              Generate a code to share with your partner or enter the code they
              shared with you
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="generate" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="generate">Generate Code</TabsTrigger>
                <TabsTrigger value="enter">Enter Code</TabsTrigger>
              </TabsList>

              <TabsContent value="generate" className="space-y-4">
                <div className="mt-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <Input
                      value={generatedCode}
                      readOnly
                      className="font-mono text-center text-lg"
                      placeholder="No code generated"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={copyCodeToClipboard}
                      disabled={!generatedCode}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button
                    onClick={generateCoupleCode}
                    className="w-full"
                    disabled={loading}
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    {loading ? 'Generating...' : 'Generate New Code'}
                  </Button>
                  <p className="text-sm text-muted-foreground text-center">
                    Share this code with your partner so they can connect with
                    you
                  </p>

                  {errorMessage && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        {errorMessage}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="enter" className="space-y-4">
                <div className="mt-6 space-y-4">
                  <Input
                    value={inputCode}
                    onChange={(e) => setInputCode(e.target.value)}
                    className="font-mono text-center text-lg"
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                  />
                  <Button
                    onClick={connectWithPartner}
                    className="w-full"
                    disabled={
                      loading || connectionStatus === "pending" || inputCode.length !== 6
                    }
                  >
                    {connectionStatus === "pending" || loading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      "Connect with Partner"
                    )}
                  </Button>

                  {(connectionStatus === "error" || errorMessage) && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        {errorMessage || "Invalid code. Please check and try again."}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {activeTab === "results" && (
        <Card>
          <CardHeader>
            <CardTitle>Assessment Results</CardTitle>
            <CardDescription>
              {connectionStatus === "connected"
                ? "View your compatibility results and insights"
                : "Connect with your partner to view compatibility results"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {connectionStatus === "connected" ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  {completedDomains.length === domains.length
                    ? "All assessments completed! View your detailed results below."
                    : "Complete all assessment domains to view detailed compatibility results."}
                </p>
                <Button disabled={completedDomains.length !== domains.length}>
                  View Detailed Results
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  You need to connect with your partner first to view
                  compatibility results.
                </p>
                <Button onClick={() => setActiveTab("connect")}>
                  Connect with Partner
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AssessmentDashboard;
