export type Json =
    | string
    | number
    | boolean
    | null
    | { [key: string]: Json | undefined }
    | Json[];

export type Database = {
    public: {
        Tables: {
            admin_users: {
                Row: {
                    created_at: string | null;
                    id: string;
                    updated_at: string | null;
                    user_id: string;
                };
                Insert: {
                    created_at?: string | null;
                    id?: string;
                    updated_at?: string | null;
                    user_id: string;
                };
                Update: {
                    created_at?: string | null;
                    id?: string;
                    updated_at?: string | null;
                    user_id?: string;
                };
                Relationships: [];
            };
            counseling_sessions: {
                Row: {
                    counselor_id: string;
                    couple_id: string;
                    created_at: string | null;
                    duration_minutes: number | null;
                    id: string;
                    notes: string | null;
                    session_date: string;
                    status: string | null;
                    updated_at: string | null;
                };
                Insert: {
                    counselor_id: string;
                    couple_id: string;
                    created_at?: string | null;
                    duration_minutes?: number | null;
                    id?: string;
                    notes?: string | null;
                    session_date: string;
                    status?: string | null;
                    updated_at?: string | null;
                };
                Update: {
                    counselor_id?: string;
                    couple_id?: string;
                    created_at?: string | null;
                    duration_minutes?: number | null;
                    id?: string;
                    notes?: string | null;
                    session_date?: string;
                    status?: string | null;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "counseling_sessions_couple_id_fkey";
                        columns: ["couple_id"];
                        isOneToOne: false;
                        referencedRelation: "couples";
                        referencedColumns: ["couple_id"];
                    },
                ];
            };
            counselor_couple_assignments: {
                Row: {
                    assigned_at: string | null;
                    counselor_id: string;
                    couple_id: string;
                    id: string;
                    status: string | null;
                };
                Insert: {
                    assigned_at?: string | null;
                    counselor_id: string;
                    couple_id: string;
                    id?: string;
                    status?: string | null;
                };
                Update: {
                    assigned_at?: string | null;
                    counselor_id?: string;
                    couple_id?: string;
                    id?: string;
                    status?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "counselor_couple_assignments_couple_id_fkey";
                        columns: ["couple_id"];
                        isOneToOne: false;
                        referencedRelation: "couples";
                        referencedColumns: ["couple_id"];
                    },
                ];
            };
            counselor_profiles: {
                Row: {
                    bio: string | null;
                    created_at: string | null;
                    full_name: string | null;
                    id: string;
                    profile_image_url: string | null;
                    specialization: string | null;
                    updated_at: string | null;
                    user_id: string;
                    years_experience: number | null;
                };
                Insert: {
                    bio?: string | null;
                    created_at?: string | null;
                    full_name?: string | null;
                    id?: string;
                    profile_image_url?: string | null;
                    specialization?: string | null;
                    updated_at?: string | null;
                    user_id: string;
                    years_experience?: number | null;
                };
                Update: {
                    bio?: string | null;
                    created_at?: string | null;
                    full_name?: string | null;
                    id?: string;
                    profile_image_url?: string | null;
                    specialization?: string | null;
                    updated_at?: string | null;
                    user_id?: string;
                    years_experience?: number | null;
                };
                Relationships: [];
            };
            couple_results: {
                Row: {
                    alignment_areas: Json;
                    compatibility_scores: Json;
                    conflict_areas: Json;
                    couple_id: string;
                    created_at: string | null;
                    id: string;
                    overall_compatibility: number;
                    updated_at: string | null;
                };
                Insert: {
                    alignment_areas: Json;
                    compatibility_scores: Json;
                    conflict_areas: Json;
                    couple_id: string;
                    created_at?: string | null;
                    id?: string;
                    overall_compatibility: number;
                    updated_at?: string | null;
                };
                Update: {
                    alignment_areas?: Json;
                    compatibility_scores?: Json;
                    conflict_areas?: Json;
                    couple_id?: string;
                    created_at?: string | null;
                    id?: string;
                    overall_compatibility?: number;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "couple_results_couple_id_fkey";
                        columns: ["couple_id"];
                        isOneToOne: false;
                        referencedRelation: "couples";
                        referencedColumns: ["couple_id"];
                    },
                ];
            };
            couple_invitation_codes: {
                Row: {
                    code: string;
                    created_at: string | null;
                    creator_user_id: string;
                    expires_at: string;
                    id: string;
                    is_active: boolean | null;
                    updated_at: string | null;
                    used_at: string | null;
                    used_by_user_id: string | null;
                };
                Insert: {
                    code: string;
                    created_at?: string | null;
                    creator_user_id: string;
                    expires_at?: string;
                    id?: string;
                    is_active?: boolean | null;
                    updated_at?: string | null;
                    used_at?: string | null;
                    used_by_user_id?: string | null;
                };
                Update: {
                    code?: string;
                    created_at?: string | null;
                    creator_user_id?: string;
                    expires_at?: string;
                    id?: string;
                    is_active?: boolean | null;
                    updated_at?: string | null;
                    used_at?: string | null;
                    used_by_user_id?: string | null;
                };
                Relationships: [];
            };
            couples: {
                Row: {
                    couple_id: string;
                    created_at: string | null;
                    updated_at: string | null;
                    user_id_1: string;
                    user_id_2: string;
                };
                Insert: {
                    couple_id?: string;
                    created_at?: string | null;
                    updated_at?: string | null;
                    user_id_1: string;
                    user_id_2: string;
                };
                Update: {
                    couple_id?: string;
                    created_at?: string | null;
                    updated_at?: string | null;
                    user_id_1?: string;
                    user_id_2?: string;
                };
                Relationships: [];
            };
            individual_results: {
                Row: {
                    created_at: string | null;
                    domains: Json;
                    id: string;
                    overall_score: number;
                    updated_at: string | null;
                    user_id: string;
                };
                Insert: {
                    created_at?: string | null;
                    domains: Json;
                    id?: string;
                    overall_score: number;
                    updated_at?: string | null;
                    user_id: string;
                };
                Update: {
                    created_at?: string | null;
                    domains?: Json;
                    id?: string;
                    overall_score?: number;
                    updated_at?: string | null;
                    user_id?: string;
                };
                Relationships: [];
            };
            profiles: {
                Row: {
                    avatar_url: string | null;
                    created_at: string | null;
                    email: string | null;
                    full_name: string | null;
                    id: string;
                    updated_at: string | null;
                };
                Insert: {
                    avatar_url?: string | null;
                    created_at?: string | null;
                    email?: string | null;
                    full_name?: string | null;
                    id: string;
                    updated_at?: string | null;
                };
                Update: {
                    avatar_url?: string | null;
                    created_at?: string | null;
                    email?: string | null;
                    full_name?: string | null;
                    id?: string;
                    updated_at?: string | null;
                };
                Relationships: [];
            };
            recommendations: {
                Row: {
                    couple_id: string;
                    created_at: string | null;
                    description: string;
                    domain: string;
                    id: string;
                    priority: string;
                    title: string;
                    updated_at: string | null;
                };
                Insert: {
                    couple_id: string;
                    created_at?: string | null;
                    description: string;
                    domain: string;
                    id?: string;
                    priority: string;
                    title: string;
                    updated_at?: string | null;
                };
                Update: {
                    couple_id?: string;
                    created_at?: string | null;
                    description?: string;
                    domain?: string;
                    id?: string;
                    priority?: string;
                    title?: string;
                    updated_at?: string | null;
                };
                Relationships: [
                    {
                        foreignKeyName: "recommendations_couple_id_fkey";
                        columns: ["couple_id"];
                        isOneToOne: false;
                        referencedRelation: "couples";
                        referencedColumns: ["couple_id"];
                    },
                ];
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            [_ in never]: never;
        };
        Enums: {
            [_ in never]: never;
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
    DefaultSchemaTableNameOrOptions extends
        | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
        | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database;
    }
        ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
              Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
        : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
          Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
          Row: infer R;
      }
        ? R
        : never
    : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
            DefaultSchema["Views"])
      ? (DefaultSchema["Tables"] &
            DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
            Row: infer R;
        }
          ? R
          : never
      : never;

export type TablesInsert<
    DefaultSchemaTableNameOrOptions extends
        | keyof DefaultSchema["Tables"]
        | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database;
    }
        ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
        : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
          Insert: infer I;
      }
        ? I
        : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
      ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
            Insert: infer I;
        }
          ? I
          : never
      : never;

export type TablesUpdate<
    DefaultSchemaTableNameOrOptions extends
        | keyof DefaultSchema["Tables"]
        | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database;
    }
        ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
        : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
          Update: infer U;
      }
        ? U
        : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
      ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
            Update: infer U;
        }
          ? U
          : never
      : never;

export type Enums<
    DefaultSchemaEnumNameOrOptions extends
        | keyof DefaultSchema["Enums"]
        | { schema: keyof Database },
    EnumName extends DefaultSchemaEnumNameOrOptions extends {
        schema: keyof Database;
    }
        ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
        : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
    : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
      ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
      : never;

export type CompositeTypes<
    PublicCompositeTypeNameOrOptions extends
        | keyof DefaultSchema["CompositeTypes"]
        | { schema: keyof Database },
    CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
        schema: keyof Database;
    }
        ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
        : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
    ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
    : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
      ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
      : never;

export const Constants = {
    public: {
        Enums: {},
    },
} as const;
