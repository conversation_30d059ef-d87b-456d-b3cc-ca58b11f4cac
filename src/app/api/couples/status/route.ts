import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET(request: Request) {
  try {
    const supabase = createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is in a couple
    const { data: couple, error: coupleError } = await supabase
      .from("couples")
      .select(`
        couple_id,
        user_id_1,
        user_id_2,
        created_at
      `)
      .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
      .single();

    if (coupleError && coupleError.code !== 'PGRST116') {
      console.error("Error fetching couple:", coupleError);
      return NextResponse.json(
        { error: "Failed to fetch couple status" },
        { status: 500 }
      );
    }

    if (!couple) {
      // User is not connected, check for active invitation codes
      const { data: activeCodes } = await supabase
        .from("couple_invitation_codes")
        .select("code, expires_at")
        .eq("creator_user_id", user.id)
        .eq("is_active", true)
        .order("created_at", { ascending: false })
        .limit(1);

      return NextResponse.json({
        isConnected: false,
        couple: null,
        partner: null,
        activeInvitationCode: activeCodes?.[0] || null
      });
    }

    // Get partner ID
    const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;

    // Get partner profile
    const { data: partnerProfile } = await supabase
      .from("profiles")
      .select("full_name, email, avatar_url")
      .eq("id", partnerId)
      .single();

    return NextResponse.json({
      isConnected: true,
      couple: {
        id: couple.couple_id,
        created_at: couple.created_at
      },
      partner: {
        id: partnerId,
        name: partnerProfile?.full_name || "Partner",
        email: partnerProfile?.email,
        avatar_url: partnerProfile?.avatar_url
      },
      activeInvitationCode: null
    });

  } catch (error) {
    console.error("Error in couples status API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
