"use client";

import React, { useState, useEffect } from "react";
import AssessmentDashboard from "@/components/assessment/AssessmentDashboard";
import { createClient } from "@/lib/supabase/client";
import { Loader2 } from "lucide-react";

export default function DashboardPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState({
    userName: "",
    coupleCode: "",
    partnerName: "",
    isConnected: false,
    completedDomains: [] as string[],
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          // Redirect to login if not authenticated
          window.location.href = "/login";
          return;
        }

        // Check user role and redirect if needed
        try {
          const response = await fetch("/api/auth");
          const userData = await response.json();

          if (userData.role === "admin") {
            window.location.href = "/admin/dashboard";
            return;
          } else if (userData.role === "counselor") {
            window.location.href = "/counselor/dashboard";
            return;
          }
        } catch (err) {
          console.error("Error checking user role:", err);
          // Continue with regular user flow if role check fails
        }

        // Get user profile
        const { data: profile } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        // Get individual results to determine completed domains
        const { data: results } = await supabase
          .from("individual_results")
          .select("domains")
          .eq("user_id", user.id)
          .single();

        // Get couple status from API
        const coupleResponse = await fetch("/api/couples/status");
        const coupleStatus = await coupleResponse.json();

        // Extract completed domains from results
        const completedDomains =
          results?.domains?.map((domain: any) => domain.domain.toLowerCase()) ||
          [];

        setUserData({
          userName: profile?.full_name || user.email?.split("@")[0] || "User",
          coupleCode: coupleStatus.activeInvitationCode?.code || "",
          partnerName: coupleStatus.partner?.name || "",
          isConnected: coupleStatus.isConnected || false,
          completedDomains,
        });
      } catch (err) {
        console.error("Error fetching user data:", err);
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while loading your dashboard",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading your dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AssessmentDashboard
        userName={userData.userName}
        coupleCode={userData.coupleCode}
        partnerName={userData.partnerName}
        isConnected={userData.isConnected}
        completedDomains={userData.completedDomains}
      />
    </div>
  );
}
