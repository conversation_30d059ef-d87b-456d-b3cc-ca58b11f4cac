"use client";

import React, { useState, useEffect } from "react";
import AssessmentDashboard from "@/components/assessment/AssessmentDashboard";
import { createClient } from "@/lib/supabase/client";
import { Loader2 } from "lucide-react";

export default function DashboardPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState({
    userName: "",
    coupleCode: "",
    partnerName: "",
    isConnected: false,
    completedDomains: [] as string[],
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          // Redirect to login if not authenticated
          window.location.href = "/login";
          return;
        }

        // Check user role and redirect if needed
        try {
          const response = await fetch("/api/auth");
          const userData = await response.json();

          if (userData.role === "admin") {
            window.location.href = "/admin/dashboard";
            return;
          } else if (userData.role === "counselor") {
            window.location.href = "/counselor/dashboard";
            return;
          }
        } catch (err) {
          console.error("Error checking user role:", err);
          // Continue with regular user flow if role check fails
        }

        // Get user profile
        const { data: profile } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        // Get individual results to determine completed domains
        const { data: results } = await supabase
          .from("individual_results")
          .select("domains")
          .eq("user_id", user.id)
          .single();

        // Get couple status directly from Supabase
        const { data: couple } = await supabase
          .from("couples")
          .select(`
            couple_id,
            user_id_1,
            user_id_2,
            created_at
          `)
          .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
          .single();

        // Get active invitation code if not connected
        let activeCode = null;
        if (!couple) {
          const { data: activeCodes } = await supabase
            .from("couple_invitation_codes")
            .select("code, expires_at")
            .eq("creator_user_id", user.id)
            .eq("is_active", true)
            .order("created_at", { ascending: false })
            .limit(1);

          activeCode = activeCodes?.[0] || null;
        }

        // Get partner info if connected
        let partnerName = "";
        if (couple) {
          const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;
          const { data: partnerProfile } = await supabase
            .from("profiles")
            .select("full_name")
            .eq("id", partnerId)
            .single();

          partnerName = partnerProfile?.full_name || "Partner";
        }

        // Extract completed domains from results
        const completedDomains =
          results?.domains?.map((domain: any) => domain.domain.toLowerCase()) ||
          [];

        setUserData({
          userName: profile?.full_name || user.email?.split("@")[0] || "User",
          coupleCode: activeCode?.code || "",
          partnerName: partnerName,
          isConnected: !!couple,
          completedDomains,
        });
      } catch (err) {
        console.error("Error fetching user data:", err);
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while loading your dashboard",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading your dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AssessmentDashboard
        userName={userData.userName}
        coupleCode={userData.coupleCode}
        partnerName={userData.partnerName}
        isConnected={userData.isConnected}
        completedDomains={userData.completedDomains}
      />
    </div>
  );
}
