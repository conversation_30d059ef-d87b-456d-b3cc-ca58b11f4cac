"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/couples/status/route";
exports.ids = ["app/api/couples/status/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_couples_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/couples/status/route.ts */ \"(rsc)/./src/app/api/couples/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/couples/status/route\",\n        pathname: \"/api/couples/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/couples/status/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Nextjs/marriage-map/src/app/api/couples/status/route.ts\",\n    nextConfigOutput,\n    userland: _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_couples_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/couples/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/couples/status/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/couples/status/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get current user\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is in a couple\n        const { data: couple, error: coupleError } = await supabase.from(\"couples\").select(`\n        couple_id,\n        user_id_1,\n        user_id_2,\n        created_at\n      `).or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`).single();\n        if (coupleError && coupleError.code !== \"PGRST116\") {\n            console.error(\"Error fetching couple:\", coupleError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch couple status\"\n            }, {\n                status: 500\n            });\n        }\n        if (!couple) {\n            // User is not connected, check for active invitation codes\n            const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                ascending: false\n            }).limit(1);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                isConnected: false,\n                couple: null,\n                partner: null,\n                activeInvitationCode: activeCodes?.[0] || null\n            });\n        }\n        // Get partner ID\n        const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n        // Get partner profile\n        const { data: partnerProfile } = await supabase.from(\"profiles\").select(\"full_name, email, avatar_url\").eq(\"id\", partnerId).single();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            isConnected: true,\n            couple: {\n                id: couple.couple_id,\n                created_at: couple.created_at\n            },\n            partner: {\n                id: partnerId,\n                name: partnerProfile?.full_name || \"Partner\",\n                email: partnerProfile?.email,\n                avatar_url: partnerProfile?.avatar_url\n            },\n            activeInvitationCode: null\n        });\n    } catch (error) {\n        console.error(\"Error in couples status API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jb3VwbGVzL3N0YXR1cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkM7QUFDVTtBQUU5QyxlQUFlRSxJQUFJQyxPQUFnQjtJQUN4QyxJQUFJO1FBQ0YsTUFBTUMsV0FBV0gsa0VBQVlBO1FBRTdCLG1CQUFtQjtRQUNuQixNQUFNLEVBQUVJLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUVDLE9BQU9DLFNBQVMsRUFBRSxHQUFHLE1BQU1KLFNBQVNLLElBQUksQ0FBQ0MsT0FBTztRQUV4RSxJQUFJRixhQUFhLENBQUNGLE1BQU07WUFDdEIsT0FBT04scURBQVlBLENBQUNXLElBQUksQ0FBQztnQkFBRUosT0FBTztZQUFlLEdBQUc7Z0JBQUVLLFFBQVE7WUFBSTtRQUNwRTtRQUVBLCtCQUErQjtRQUMvQixNQUFNLEVBQUVQLE1BQU1RLE1BQU0sRUFBRU4sT0FBT08sV0FBVyxFQUFFLEdBQUcsTUFBTVYsU0FDaERXLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMsQ0FBQzs7Ozs7TUFLVCxDQUFDLEVBQ0FDLEVBQUUsQ0FBQyxDQUFDLGFBQWEsRUFBRVgsS0FBS1ksRUFBRSxDQUFDLGNBQWMsRUFBRVosS0FBS1ksRUFBRSxDQUFDLENBQUMsRUFDcERDLE1BQU07UUFFVCxJQUFJTCxlQUFlQSxZQUFZTSxJQUFJLEtBQUssWUFBWTtZQUNsREMsUUFBUWQsS0FBSyxDQUFDLDBCQUEwQk87WUFDeEMsT0FBT2QscURBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQUVKLE9BQU87WUFBZ0MsR0FDekM7Z0JBQUVLLFFBQVE7WUFBSTtRQUVsQjtRQUVBLElBQUksQ0FBQ0MsUUFBUTtZQUNYLDJEQUEyRDtZQUMzRCxNQUFNLEVBQUVSLE1BQU1pQixXQUFXLEVBQUUsR0FBRyxNQUFNbEIsU0FDakNXLElBQUksQ0FBQywyQkFDTEMsTUFBTSxDQUFDLG9CQUNQTyxFQUFFLENBQUMsbUJBQW1CakIsS0FBS1ksRUFBRSxFQUM3QkssRUFBRSxDQUFDLGFBQWEsTUFDaEJDLEtBQUssQ0FBQyxjQUFjO2dCQUFFQyxXQUFXO1lBQU0sR0FDdkNDLEtBQUssQ0FBQztZQUVULE9BQU8xQixxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO2dCQUN2QmdCLGFBQWE7Z0JBQ2JkLFFBQVE7Z0JBQ1JlLFNBQVM7Z0JBQ1RDLHNCQUFzQlAsYUFBYSxDQUFDLEVBQUUsSUFBSTtZQUM1QztRQUNGO1FBRUEsaUJBQWlCO1FBQ2pCLE1BQU1RLFlBQVlqQixPQUFPa0IsU0FBUyxLQUFLekIsS0FBS1ksRUFBRSxHQUFHTCxPQUFPbUIsU0FBUyxHQUFHbkIsT0FBT2tCLFNBQVM7UUFFcEYsc0JBQXNCO1FBQ3RCLE1BQU0sRUFBRTFCLE1BQU00QixjQUFjLEVBQUUsR0FBRyxNQUFNN0IsU0FDcENXLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsZ0NBQ1BPLEVBQUUsQ0FBQyxNQUFNTyxXQUNUWCxNQUFNO1FBRVQsT0FBT25CLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7WUFDdkJnQixhQUFhO1lBQ2JkLFFBQVE7Z0JBQ05LLElBQUlMLE9BQU9xQixTQUFTO2dCQUNwQkMsWUFBWXRCLE9BQU9zQixVQUFVO1lBQy9CO1lBQ0FQLFNBQVM7Z0JBQ1BWLElBQUlZO2dCQUNKTSxNQUFNSCxnQkFBZ0JJLGFBQWE7Z0JBQ25DQyxPQUFPTCxnQkFBZ0JLO2dCQUN2QkMsWUFBWU4sZ0JBQWdCTTtZQUM5QjtZQUNBVixzQkFBc0I7UUFDeEI7SUFFRixFQUFFLE9BQU90QixPQUFPO1FBQ2RjLFFBQVFkLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU9QLHFEQUFZQSxDQUFDVyxJQUFJLENBQ3RCO1lBQUVKLE9BQU87UUFBd0IsR0FDakM7WUFBRUssUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9zcmMvYXBwL2FwaS9jb3VwbGVzL3N0YXR1cy9yb3V0ZS50cz8zM2VmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkAvbGliL3N1cGFiYXNlL3NlcnZlclwiO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuXG4gICAgLy8gR2V0IGN1cnJlbnQgdXNlclxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiB1c2VyRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIFxuICAgIGlmICh1c2VyRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIlVuYXV0aG9yaXplZFwiIH0sIHsgc3RhdHVzOiA0MDEgfSk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBpbiBhIGNvdXBsZVxuICAgIGNvbnN0IHsgZGF0YTogY291cGxlLCBlcnJvcjogY291cGxlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbShcImNvdXBsZXNcIilcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICBjb3VwbGVfaWQsXG4gICAgICAgIHVzZXJfaWRfMSxcbiAgICAgICAgdXNlcl9pZF8yLFxuICAgICAgICBjcmVhdGVkX2F0XG4gICAgICBgKVxuICAgICAgLm9yKGB1c2VyX2lkXzEuZXEuJHt1c2VyLmlkfSx1c2VyX2lkXzIuZXEuJHt1c2VyLmlkfWApXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoY291cGxlRXJyb3IgJiYgY291cGxlRXJyb3IuY29kZSAhPT0gJ1BHUlNUMTE2Jykge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGNvdXBsZTpcIiwgY291cGxlRXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBjb3VwbGUgc3RhdHVzXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGlmICghY291cGxlKSB7XG4gICAgICAvLyBVc2VyIGlzIG5vdCBjb25uZWN0ZWQsIGNoZWNrIGZvciBhY3RpdmUgaW52aXRhdGlvbiBjb2Rlc1xuICAgICAgY29uc3QgeyBkYXRhOiBhY3RpdmVDb2RlcyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oXCJjb3VwbGVfaW52aXRhdGlvbl9jb2Rlc1wiKVxuICAgICAgICAuc2VsZWN0KFwiY29kZSwgZXhwaXJlc19hdFwiKVxuICAgICAgICAuZXEoXCJjcmVhdG9yX3VzZXJfaWRcIiwgdXNlci5pZClcbiAgICAgICAgLmVxKFwiaXNfYWN0aXZlXCIsIHRydWUpXG4gICAgICAgIC5vcmRlcihcImNyZWF0ZWRfYXRcIiwgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAgIC5saW1pdCgxKTtcblxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgaXNDb25uZWN0ZWQ6IGZhbHNlLFxuICAgICAgICBjb3VwbGU6IG51bGwsXG4gICAgICAgIHBhcnRuZXI6IG51bGwsXG4gICAgICAgIGFjdGl2ZUludml0YXRpb25Db2RlOiBhY3RpdmVDb2Rlcz8uWzBdIHx8IG51bGxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEdldCBwYXJ0bmVyIElEXG4gICAgY29uc3QgcGFydG5lcklkID0gY291cGxlLnVzZXJfaWRfMSA9PT0gdXNlci5pZCA/IGNvdXBsZS51c2VyX2lkXzIgOiBjb3VwbGUudXNlcl9pZF8xO1xuXG4gICAgLy8gR2V0IHBhcnRuZXIgcHJvZmlsZVxuICAgIGNvbnN0IHsgZGF0YTogcGFydG5lclByb2ZpbGUgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbShcInByb2ZpbGVzXCIpXG4gICAgICAuc2VsZWN0KFwiZnVsbF9uYW1lLCBlbWFpbCwgYXZhdGFyX3VybFwiKVxuICAgICAgLmVxKFwiaWRcIiwgcGFydG5lcklkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGlzQ29ubmVjdGVkOiB0cnVlLFxuICAgICAgY291cGxlOiB7XG4gICAgICAgIGlkOiBjb3VwbGUuY291cGxlX2lkLFxuICAgICAgICBjcmVhdGVkX2F0OiBjb3VwbGUuY3JlYXRlZF9hdFxuICAgICAgfSxcbiAgICAgIHBhcnRuZXI6IHtcbiAgICAgICAgaWQ6IHBhcnRuZXJJZCxcbiAgICAgICAgbmFtZTogcGFydG5lclByb2ZpbGU/LmZ1bGxfbmFtZSB8fCBcIlBhcnRuZXJcIixcbiAgICAgICAgZW1haWw6IHBhcnRuZXJQcm9maWxlPy5lbWFpbCxcbiAgICAgICAgYXZhdGFyX3VybDogcGFydG5lclByb2ZpbGU/LmF2YXRhcl91cmxcbiAgICAgIH0sXG4gICAgICBhY3RpdmVJbnZpdGF0aW9uQ29kZTogbnVsbFxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGluIGNvdXBsZXMgc3RhdHVzIEFQSTpcIiwgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6IFwiSW50ZXJuYWwgc2VydmVyIGVycm9yXCIgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVDbGllbnQiLCJHRVQiLCJyZXF1ZXN0Iiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImVycm9yIiwidXNlckVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJqc29uIiwic3RhdHVzIiwiY291cGxlIiwiY291cGxlRXJyb3IiLCJmcm9tIiwic2VsZWN0Iiwib3IiLCJpZCIsInNpbmdsZSIsImNvZGUiLCJjb25zb2xlIiwiYWN0aXZlQ29kZXMiLCJlcSIsIm9yZGVyIiwiYXNjZW5kaW5nIiwibGltaXQiLCJpc0Nvbm5lY3RlZCIsInBhcnRuZXIiLCJhY3RpdmVJbnZpdGF0aW9uQ29kZSIsInBhcnRuZXJJZCIsInVzZXJfaWRfMSIsInVzZXJfaWRfMiIsInBhcnRuZXJQcm9maWxlIiwiY291cGxlX2lkIiwiY3JlYXRlZF9hdCIsIm5hbWUiLCJmdWxsX25hbWUiLCJlbWFpbCIsImF2YXRhcl91cmwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/couples/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://eqghwtejdnzgopmcjlho.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value: \"\",\n                        ...options\n                    });\n                } catch (error) {\n                // The `delete` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();