"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/assessment/AssessmentDashboard */ \"(app-pages-browser)/./src/components/assessment/AssessmentDashboard.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userName: \"\",\n        coupleCode: \"\",\n        partnerName: \"\",\n        isConnected: false,\n        completedDomains: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            try {\n                var _results_domains, _user_email;\n                setLoading(true);\n                const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n                // Get current user\n                const { data: { user } } = await supabase.auth.getUser();\n                if (!user) {\n                    // Redirect to login if not authenticated\n                    window.location.href = \"/login\";\n                    return;\n                }\n                // Check user role and redirect if needed\n                try {\n                    const response = await fetch(\"/api/auth\");\n                    const userData = await response.json();\n                    if (userData.role === \"admin\") {\n                        window.location.href = \"/admin/dashboard\";\n                        return;\n                    } else if (userData.role === \"counselor\") {\n                        window.location.href = \"/counselor/dashboard\";\n                        return;\n                    }\n                } catch (err) {\n                    console.error(\"Error checking user role:\", err);\n                // Continue with regular user flow if role check fails\n                }\n                // Get user profile\n                const { data: profile } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n                // Get individual results to determine completed domains\n                const { data: results, error: resultsError } = await supabase.from(\"individual_results\").select(\"domains\").eq(\"user_id\", user.id).maybeSingle();\n                console.log(\"Dashboard - Results query:\", {\n                    results,\n                    resultsError\n                });\n                // Get couple status directly from Supabase\n                const { data: couple, error: coupleError } = await supabase.from(\"couples\").select(\"\\n            couple_id,\\n            user_id_1,\\n            user_id_2,\\n            created_at\\n          \").or(\"user_id_1.eq.\".concat(user.id, \",user_id_2.eq.\").concat(user.id)).single();\n                console.log(\"Dashboard - User ID:\", user.id);\n                console.log(\"Dashboard - Couple query result:\", {\n                    couple,\n                    coupleError\n                });\n                // Get active invitation code if not connected\n                let activeCode = null;\n                if (!couple) {\n                    const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                        ascending: false\n                    }).limit(1);\n                    activeCode = (activeCodes === null || activeCodes === void 0 ? void 0 : activeCodes[0]) || null;\n                }\n                // Get partner info if connected\n                let partnerName = \"\";\n                if (couple) {\n                    const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n                    const { data: partnerProfile } = await supabase.from(\"profiles\").select(\"full_name\").eq(\"id\", partnerId).single();\n                    partnerName = (partnerProfile === null || partnerProfile === void 0 ? void 0 : partnerProfile.full_name) || \"Partner\";\n                }\n                // Extract completed domains from results\n                const completedDomains = (results === null || results === void 0 ? void 0 : (_results_domains = results.domains) === null || _results_domains === void 0 ? void 0 : _results_domains.map((domain)=>domain.domain.toLowerCase())) || [];\n                setUserData({\n                    userName: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n                    coupleCode: (activeCode === null || activeCode === void 0 ? void 0 : activeCode.code) || \"\",\n                    partnerName: partnerName,\n                    isConnected: !!couple,\n                    completedDomains\n                });\n            } catch (err) {\n                console.error(\"Error fetching user data:\", err);\n                setError(err instanceof Error ? err.message : \"An error occurred while loading your dashboard\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            userName: userData.userName,\n            coupleCode: userData.coupleCode,\n            partnerName: userData.partnerName,\n            isConnected: userData.isConnected,\n            completedDomains: userData.completedDomains\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"31gDMWiYh1dxcaec2kpQ9DDsP9I=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});