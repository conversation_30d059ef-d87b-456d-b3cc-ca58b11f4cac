"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/assessment/AssessmentDashboard */ \"(app-pages-browser)/./src/components/assessment/AssessmentDashboard.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userName: \"\",\n        coupleCode: \"\",\n        partnerName: \"\",\n        isConnected: false,\n        completedDomains: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            try {\n                var _results_domains, _user_email;\n                setLoading(true);\n                const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n                // Get current user\n                const { data: { user } } = await supabase.auth.getUser();\n                if (!user) {\n                    // Redirect to login if not authenticated\n                    window.location.href = \"/login\";\n                    return;\n                }\n                // Check user role and redirect if needed\n                try {\n                    const response = await fetch(\"/api/auth\");\n                    const userData = await response.json();\n                    if (userData.role === \"admin\") {\n                        window.location.href = \"/admin/dashboard\";\n                        return;\n                    } else if (userData.role === \"counselor\") {\n                        window.location.href = \"/counselor/dashboard\";\n                        return;\n                    }\n                } catch (err) {\n                    console.error(\"Error checking user role:\", err);\n                // Continue with regular user flow if role check fails\n                }\n                // Get user profile\n                const { data: profile, error: profileError } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).maybeSingle();\n                console.log(\"Dashboard - Profile query:\", {\n                    profile,\n                    profileError\n                });\n                // Get individual results to determine completed domains\n                const { data: results, error: resultsError } = await supabase.from(\"individual_results\").select(\"domains\").eq(\"user_id\", user.id).maybeSingle();\n                console.log(\"Dashboard - Results query:\", {\n                    results,\n                    resultsError\n                });\n                // Get couple status directly from Supabase - simplified query\n                const { data: couples, error: coupleError } = await supabase.from(\"couples\").select(\"couple_id, user_id_1, user_id_2, created_at\");\n                console.log(\"Dashboard - All couples query:\", {\n                    couples,\n                    coupleError\n                });\n                // Find couple for current user\n                const couple = (couples === null || couples === void 0 ? void 0 : couples.find((c)=>c.user_id_1 === user.id || c.user_id_2 === user.id)) || null;\n                console.log(\"Dashboard - User ID:\", user.id);\n                console.log(\"Dashboard - Couple query result:\", {\n                    couple,\n                    coupleError\n                });\n                // Get active invitation code if not connected\n                let activeCode = null;\n                if (!couple) {\n                    const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                        ascending: false\n                    }).limit(1);\n                    activeCode = (activeCodes === null || activeCodes === void 0 ? void 0 : activeCodes[0]) || null;\n                }\n                // Get partner info if connected\n                let partnerName = \"\";\n                if (couple) {\n                    const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n                    const { data: partnerProfile } = await supabase.from(\"profiles\").select(\"full_name\").eq(\"id\", partnerId).maybeSingle();\n                    partnerName = (partnerProfile === null || partnerProfile === void 0 ? void 0 : partnerProfile.full_name) || \"Partner\";\n                }\n                // Extract completed domains from results\n                const completedDomains = (results === null || results === void 0 ? void 0 : (_results_domains = results.domains) === null || _results_domains === void 0 ? void 0 : _results_domains.map((domain)=>domain.domain.toLowerCase())) || [];\n                setUserData({\n                    userName: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n                    coupleCode: (activeCode === null || activeCode === void 0 ? void 0 : activeCode.code) || \"\",\n                    partnerName: partnerName,\n                    isConnected: !!couple,\n                    completedDomains\n                });\n            } catch (err) {\n                console.error(\"Error fetching user data:\", err);\n                setError(err instanceof Error ? err.message : \"An error occurred while loading your dashboard\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            userName: userData.userName,\n            coupleCode: userData.coupleCode,\n            partnerName: userData.partnerName,\n            isConnected: userData.isConnected,\n            completedDomains: userData.completedDomains\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"31gDMWiYh1dxcaec2kpQ9DDsP9I=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});