"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/assessment/AssessmentDashboard */ \"(app-pages-browser)/./src/components/assessment/AssessmentDashboard.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userName: \"\",\n        coupleCode: \"\",\n        partnerName: \"\",\n        isConnected: false,\n        completedDomains: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            try {\n                var _results_domains, _user_email;\n                setLoading(true);\n                const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n                // Get current user\n                const { data: { user } } = await supabase.auth.getUser();\n                if (!user) {\n                    // Redirect to login if not authenticated\n                    window.location.href = \"/login\";\n                    return;\n                }\n                // Check user role and redirect if needed\n                try {\n                    const response = await fetch(\"/api/auth\");\n                    const userData = await response.json();\n                    if (userData.role === \"admin\") {\n                        window.location.href = \"/admin/dashboard\";\n                        return;\n                    } else if (userData.role === \"counselor\") {\n                        window.location.href = \"/counselor/dashboard\";\n                        return;\n                    }\n                } catch (err) {\n                    console.error(\"Error checking user role:\", err);\n                // Continue with regular user flow if role check fails\n                }\n                // Get user profile\n                const { data: profile, error: profileError } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).maybeSingle();\n                console.log(\"Dashboard - Profile query:\", {\n                    profile,\n                    profileError\n                });\n                // Get individual results to determine completed domains\n                const { data: results, error: resultsError } = await supabase.from(\"individual_results\").select(\"domains\").eq(\"user_id\", user.id).maybeSingle();\n                console.log(\"Dashboard - Results query:\", {\n                    results,\n                    resultsError\n                });\n                // Get couple status directly from Supabase\n                const { data: couple, error: coupleError } = await supabase.from(\"couples\").select(\"\\n            couple_id,\\n            user_id_1,\\n            user_id_2,\\n            created_at\\n          \").or(\"user_id_1.eq.\".concat(user.id, \",user_id_2.eq.\").concat(user.id)).maybeSingle();\n                console.log(\"Dashboard - User ID:\", user.id);\n                console.log(\"Dashboard - Couple query result:\", {\n                    couple,\n                    coupleError\n                });\n                // Get active invitation code if not connected\n                let activeCode = null;\n                if (!couple) {\n                    const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                        ascending: false\n                    }).limit(1);\n                    activeCode = (activeCodes === null || activeCodes === void 0 ? void 0 : activeCodes[0]) || null;\n                }\n                // Get partner info if connected\n                let partnerName = \"\";\n                if (couple) {\n                    const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n                    const { data: partnerProfile } = await supabase.from(\"profiles\").select(\"full_name\").eq(\"id\", partnerId).maybeSingle();\n                    partnerName = (partnerProfile === null || partnerProfile === void 0 ? void 0 : partnerProfile.full_name) || \"Partner\";\n                }\n                // Extract completed domains from results\n                const completedDomains = (results === null || results === void 0 ? void 0 : (_results_domains = results.domains) === null || _results_domains === void 0 ? void 0 : _results_domains.map((domain)=>domain.domain.toLowerCase())) || [];\n                setUserData({\n                    userName: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n                    coupleCode: (activeCode === null || activeCode === void 0 ? void 0 : activeCode.code) || \"\",\n                    partnerName: partnerName,\n                    isConnected: !!couple,\n                    completedDomains\n                });\n            } catch (err) {\n                console.error(\"Error fetching user data:\", err);\n                setError(err instanceof Error ? err.message : \"An error occurred while loading your dashboard\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            userName: userData.userName,\n            coupleCode: userData.coupleCode,\n            partnerName: userData.partnerName,\n            isConnected: userData.isConnected,\n            completedDomains: userData.completedDomains\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"31gDMWiYh1dxcaec2kpQ9DDsP9I=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});