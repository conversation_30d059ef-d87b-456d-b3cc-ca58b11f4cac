"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/assessment/AssessmentDashboard */ \"(app-pages-browser)/./src/components/assessment/AssessmentDashboard.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userName: \"\",\n        coupleCode: \"\",\n        partnerName: \"\",\n        isConnected: false,\n        completedDomains: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            try {\n                var _results_domains, _user_email;\n                setLoading(true);\n                const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n                // Get current user\n                const { data: { user } } = await supabase.auth.getUser();\n                if (!user) {\n                    // Redirect to login if not authenticated\n                    window.location.href = \"/login\";\n                    return;\n                }\n                // Check user role and redirect if needed\n                try {\n                    const response = await fetch(\"/api/auth\");\n                    const userData = await response.json();\n                    if (userData.role === \"admin\") {\n                        window.location.href = \"/admin/dashboard\";\n                        return;\n                    } else if (userData.role === \"counselor\") {\n                        window.location.href = \"/counselor/dashboard\";\n                        return;\n                    }\n                } catch (err) {\n                    console.error(\"Error checking user role:\", err);\n                // Continue with regular user flow if role check fails\n                }\n                // Get user profile\n                const { data: profile } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n                // Get individual results to determine completed domains\n                const { data: results } = await supabase.from(\"individual_results\").select(\"domains\").eq(\"user_id\", user.id).single();\n                // Get couple status directly from Supabase\n                const { data: couple } = await supabase.from(\"couples\").select(\"\\n            couple_id,\\n            user_id_1,\\n            user_id_2,\\n            created_at\\n          \").or(\"user_id_1.eq.\".concat(user.id, \",user_id_2.eq.\").concat(user.id)).single();\n                // Get active invitation code if not connected\n                let activeCode = null;\n                if (!couple) {\n                    const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                        ascending: false\n                    }).limit(1);\n                    activeCode = (activeCodes === null || activeCodes === void 0 ? void 0 : activeCodes[0]) || null;\n                }\n                // Get partner info if connected\n                let partnerName = \"\";\n                if (couple) {\n                    const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n                    const { data: partnerProfile } = await supabase.from(\"profiles\").select(\"full_name\").eq(\"id\", partnerId).single();\n                    partnerName = (partnerProfile === null || partnerProfile === void 0 ? void 0 : partnerProfile.full_name) || \"Partner\";\n                }\n                // Extract completed domains from results\n                const completedDomains = (results === null || results === void 0 ? void 0 : (_results_domains = results.domains) === null || _results_domains === void 0 ? void 0 : _results_domains.map((domain)=>domain.domain.toLowerCase())) || [];\n                setUserData({\n                    userName: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n                    coupleCode: (activeCode === null || activeCode === void 0 ? void 0 : activeCode.code) || \"\",\n                    partnerName: partnerName,\n                    isConnected: !!couple,\n                    completedDomains\n                });\n            } catch (err) {\n                console.error(\"Error fetching user data:\", err);\n                setError(err instanceof Error ? err.message : \"An error occurred while loading your dashboard\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assessment_AssessmentDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            userName: userData.userName,\n            coupleCode: userData.coupleCode,\n            partnerName: userData.partnerName,\n            isConnected: userData.isConnected,\n            completedDomains: userData.completedDomains\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"31gDMWiYh1dxcaec2kpQ9DDsP9I=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});