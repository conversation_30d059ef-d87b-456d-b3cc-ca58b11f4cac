-- Create profiles table for all users
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  email TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create couple invitation codes table
CREATE TABLE IF NOT EXISTS couple_invitation_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT UNIQUE NOT NULL,
  creator_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  used_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  used_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on new tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE couple_invitation_codes ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Create policies for couple_invitation_codes
DROP POLICY IF EXISTS "Users can view their own codes" ON couple_invitation_codes;
CREATE POLICY "Users can view their own codes"
  ON couple_invitation_codes FOR SELECT
  USING (auth.uid() = creator_user_id OR auth.uid() = used_by_user_id);

DROP POLICY IF EXISTS "Users can create invitation codes" ON couple_invitation_codes;
CREATE POLICY "Users can create invitation codes"
  ON couple_invitation_codes FOR INSERT
  WITH CHECK (auth.uid() = creator_user_id);

DROP POLICY IF EXISTS "Users can update codes they use" ON couple_invitation_codes;
CREATE POLICY "Users can update codes they use"
  ON couple_invitation_codes FOR UPDATE
  USING (auth.uid() = used_by_user_id OR auth.uid() = creator_user_id);

-- Add couples table policies if not exists
DROP POLICY IF EXISTS "Users can view their couples" ON couples;
CREATE POLICY "Users can view their couples"
  ON couples FOR SELECT
  USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

DROP POLICY IF EXISTS "Users can create couples" ON couples;
CREATE POLICY "Users can create couples"
  ON couples FOR INSERT
  WITH CHECK (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

-- Enable realtime for new tables
ALTER PUBLICATION supabase_realtime ADD TABLE profiles;
ALTER PUBLICATION supabase_realtime ADD TABLE couple_invitation_codes;
ALTER PUBLICATION supabase_realtime ADD TABLE couples;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_couple_invitation_codes_code ON couple_invitation_codes(code);
CREATE INDEX IF NOT EXISTS idx_couple_invitation_codes_creator ON couple_invitation_codes(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_couple_invitation_codes_active ON couple_invitation_codes(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_couples_user_id_1 ON couples(user_id_1);
CREATE INDEX IF NOT EXISTS idx_couples_user_id_2 ON couples(user_id_2);
